<!--
Generyczny komponent Modal

Przykład użycia:

1. Podstawowy modal z formularzem:
<Modal
  v-model="showModal"
  title="Dodaj użytkownika"
  @submit="handleSubmit"
  @close="handleClose"
>
  <v-text-field
    v-model="user.name"
    label="Nazwa"
    required
  />
  <v-text-field
    v-model="user.email"
    label="Email"
    required
  />
</Modal>

2. Modal bez formularza (tylko <PERSON>awa<PERSON>):
<Modal
  v-model="showModal"
  title="Informacje"
  :has-form="false"
  :show-confirm-button="false"
  cancel-button-text="Zamknij"
>
  <p>Treść informacyjna...</p>
</Modal>

3. Modal z niestandardowymi przyciskami:
<Modal
  v-model="showModal"
  title="Potwierdź akcję"
  confirm-button-text="Zatwierdź"
  cancel-button-text="Anuluj"
  :loading="isLoading"
  @submit="handleConfirm"
>
  <template #actions>
    <v-btn @click="close">Anuluj</v-btn>
    <v-btn color="warning" @click="handleDelete">Usuń</v-btn>
    <v-btn color="primary" @click="submit">Zapisz</v-btn>
  </template>
</Modal>

Props:
- title (required): Tytuł modala
- confirmButtonText: Tekst przycisku zatwierdzającego (domyślnie "Zapisz")
- cancelButtonText: Tekst przycisku anulującego (domyślnie "Anuluj")
- loading: Stan ładowania (blokuje interakcję)
- maxWidth: Maksymalna szerokość modala (domyślnie "800")
- fullscreen: Czy modal ma być pełnoekranowy
- scrollable: Czy zawartość ma być przewijalna
- hasForm: Czy zawartość zawiera formularz (domyślnie true)
- showActions: Czy pokazywać sekcję przycisków (domyślnie true)
- showConfirmButton: Czy pokazywać przycisk zatwierdzający (domyślnie true)
- autoClearForm: Czy automatycznie czyścić formularz przy zamknięciu (domyślnie true)

Events:
- submit: Emitowany po kliknięciu przycisku zatwierdzającego i pomyślnej walidacji
- beforeSubmit: Emitowany przed walidacją
- close: Emitowany przy zamknięciu modala
- beforeClose: Emitowany przed zamknięciem modala

Sloty:
- default: Główna zawartość modala
- title: Niestandardowy tytuł (zastępuje domyślny)
- actions: Niestandardowe przyciski akcji
- before: Zawartość przed modalem
-->

<template>
  <div>
    <slot name="before" />
    <v-dialog
      v-model="show"
      :max-width="maxWidth"
      :persistent="loader"
      :fullscreen="fullscreen"
      :scrollable="scrollable"
    >
      <v-card :loading="loader">
        <v-card-title class="title">
          <slot name="title">
            <span class="headline text-uppercase text-h5">{{ title }}</span>
            <v-spacer />
            <v-btn
              icon
              text
              tile
              small
              dark
              @click="close"
            >
              <v-icon>
                mdi-close
              </v-icon>
            </v-btn>
          </slot>
        </v-card-title>
        <v-card-text>
          <v-form
            v-if="hasForm"
            ref="form"
            @submit="submit"
          >
            <slot />
          </v-form>
          <template v-else>
            <slot />
          </template>
        </v-card-text>
        <v-card-actions v-if="showActions">
          <slot name="actions">
            <v-spacer />
            <v-btn
              color="gray"
              text
              :disabled="loader"
              @click="close"
            >
              {{ cancelButtonText || $t('actions.cancel') }}
            </v-btn>
            <v-btn
              v-if="showConfirmButton"
              color="primary"
              :loading="loading"
              :disabled="loader"
              @click="submit"
            >
              {{ confirmButtonText || $t('actions.save') }}
            </v-btn>
          </slot>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import ModalMixin from '@components/common/mixins/ModalMixin.vue';

export default {
  name: 'Modal',
  mixins: [ModalMixin],
  props: {
    title: {
      type: String,
      required: true,
    },
    confirmButtonText: {
      type: String,
      default: null,
    },
    cancelButtonText: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    maxWidth: {
      type: [String, Number],
      default: '800',
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    scrollable: {
      type: Boolean,
      default: false,
    },
    hasForm: {
      type: Boolean,
      default: true,
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    showConfirmButton: {
      type: Boolean,
      default: true,
    },
    autoClearForm: {
      type: Boolean,
      default: true,
    },
  },
  data: () => ({
    loadingInternal: false,
  }),
  computed: {
    loader() {
      return this.loading || this.loadingInternal;
    },
  },
  methods: {
    validate() {
      if (this.hasForm && this.$refs.form) {
        return this.$refs.form.validate();
      }
      return true;
    },
    clear() {
      if (this.hasForm && this.$refs.form) {
        this.$refs.form.reset();
        this.$refs.form.resetValidation();
      }
    },
    beforeSubmit() {
      this.$emit('beforeSubmit');
    },
    submit() {
      this.beforeSubmit();
      if (this.validate()) {
        this.$emit('submit');
      }
    },
    beforeClose() {
      if (this.autoClearForm) {
        this.$nextTick(() => {
          this.clear();
        });
      }
      this.$emit('beforeClose');
    },
    afterClose() {
      this.$emit('close');
      this.$emit('input', false);
    },
  },
};
</script>
