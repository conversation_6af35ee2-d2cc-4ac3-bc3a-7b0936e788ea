<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      :fullscreen="$vuetify.breakpoint.smAndDown"
      max-width="700px"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">{{ $t('actions.invite_user') }}</h5>
          </span>
        </v-card-title>
        <v-card-text class="pt-6">
          <v-layout wrap>
            <v-col
              cols="12"
              sm="12"
              md="12"
            >
              <v-alert
                :value="true"
                border="left"
                type="info"
                color="blue lighten-4"
                class="blue--text text--darken-2"
              >
                {{ $t('loyalApp_invitationInfo') }}
              </v-alert>
            </v-col>
          </v-layout>
          <v-container grid-list-md>
            <v-form
              ref="form"
              v-model="form.valid"
              :lazy-validation="true"
            >
              <v-layout wrap>
                <v-col
                  cols="12"
                  sm="12"
                  md="12"
                >
                  <v-text-field
                    v-model="email"
                    prepend-icon="mdi-account"
                    :label="$t('common_email') + ' *'"
                    :rules="form.validationRules.email"
                    required
                    :validate-on-blur="form.validateOnBlur"
                  />
                  <v-autocomplete
                    v-model="selectedPackage"
                    :items="packagesOptions"
                    item-value="id"
                    item-text="displayText"
                    prepend-icon="mdi-cash"
                    label="Pakiet promocyjny"
                    :loading="packagesLoader"
                    :disabled="packagesLoader"
                    clearable
                  />

                  <v-expansion-panels
                    v-model="invoiceDataExpanded"
                    class="mb-3"
                  >
                    <v-expansion-panel>
                      <v-expansion-panel-header>
                        <v-row>
                          <v-icon>mdi-file-document-edit-outline</v-icon>
                          <h6 class="text-h6 ml-2">
                            Dane do faktury
                          </h6>
                        </v-row>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <v-text-field
                          v-model="invoiceData.taxNumber"
                          prepend-icon="mdi-card-account-details"
                          label="Numer NIP"
                          :validate-on-blur="form.validateOnBlur"
                        />
                        <v-text-field
                          v-model="invoiceData.name"
                          prepend-icon="mdi-domain"
                          label="Nazwa firmy"
                          :validate-on-blur="form.validateOnBlur"
                        />
                        <v-text-field
                          v-model="invoiceData.address"
                          prepend-icon="mdi-map-marker"
                          label="Adres"
                          :validate-on-blur="form.validateOnBlur"
                        />
                        <v-text-field
                          v-model="invoiceData.postCode"
                          prepend-icon="mdi-mailbox"
                          label="Kod pocztowy"
                          :validate-on-blur="form.validateOnBlur"
                        />
                        <v-text-field
                          v-model="invoiceData.city"
                          prepend-icon="mdi-city"
                          label="Miasto"
                          :validate-on-blur="form.validateOnBlur"
                        />
                        <v-select
                          v-model="invoiceData.country"
                          :items="countries"
                          item-text="name"
                          item-value="shortName"
                          prepend-icon="mdi-earth"
                          label="Kraj"
                          :loading="countriesLoader"
                          :disabled="countriesLoader"
                          clearable
                          :validate-on-blur="form.validateOnBlur"
                        />
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>

                  <small>*{{ $t('common_fieldRequired') }}</small>
                </v-col>
              </v-layout>
            </v-form>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="grey darken-1"
            :disabled="loaders.submit"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loaders.submit"
            @click.native="submit"
          >
            {{ $t('actions.send_invitation') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  mixins: [
    SnackbarMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      loaders: {
        submit: false,
      },
      email: '',
      selectedPackage: null,
      packages: [],
      packagesLoader: false,
      invoiceDataExpanded: null,
      countries: [],
      countriesLoader: false,
      invoiceData: {
        taxNumber: '',
        name: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      },
      form: {
        validateOnBlur: true,
        valid: true,
        validationRules: {
          email: [
            (v) => /^([a-zA-Z0-9_\-.+]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/.test(
              v,
            )
              || this.$t('loyalApp_invalidValue'),
          ],
        },
      },
    };
  },
  computed: {
    packagesOptions() {
      return this.packages.map((pkg) => ({
        ...pkg,
        displayText: `${pkg.title} (wartość: ${pkg.package_value})`,
      }));
    },
  },
  created() {
    this.getPackages();
    this.getCountries();
  },
  methods: {
    submit() {
      this.form.validateOnBlur = false;
      if (this.$refs.form.validate()) {
        this.loaders.submit = true;
        // Na razie zapisujemy dane faktury lokalnie (bez przesyłania do API)
        // Dane faktury dostępne w this.invoiceData

        this.axios
          .post('/api/gateway/wla-admin/users/invite', {
            email: this.email,
            app: this.app,
            packageId: this.selectedPackage,
            // invoiceData: this.invoiceData, // Dodamy w przyszłości
          })
          .then(
            (response) => {
              if (response.status === 200) {
                this.closeDialog();
                // this.$eventHub.$emit(
                //   'update-loyalty-user-on-loyalty-app-user-list',
                //   response.data,
                // );
              }
              this.showSnackbar(
                'success',
                this.$t('common_actionSucced'),
              );
              this.closeDialog();
            },
            (error) => {
              if (error.request && error.request.status === 409) {
                this.showSnackbar(
                  'warning',
                  this.$t('loyalApp_userAlreadyExists'),
                );
              } else if (error.request && error.request.status === 400) {
                this.showSnackbar(
                  'warning',
                  this.$t('loyalApp_accessDenied'),
                );
              }
              // on error
              this.closeDialog();
            },
          );
      }
    },
    onClose() {
      // this.$validator.reset();
      this.loaders.submit = false;
      this.dialog = false;
    },
    closeDialog() {
      // this.$validator.reset();
      this.loaders.submit = false;
      this.dialog = false;
      this.clearFormData();
    },
    clearFormData() {
      this.email = '';
      this.selectedPackage = null;
      this.invoiceDataExpanded = null;
      this.invoiceData = {
        taxNumber: '',
        name: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      };
    },
    async getPackages() {
      this.packagesLoader = true;
      try {
        const response = await this.axios.get('/api/loyalapp/packages', {
          params: {
            app: this.app,
          },
        });
        if (response.status === 200 && response.data.items) {
          this.packages = response.data.items.filter((pkg) => pkg.active);
        }
      } catch (error) {
        this.showSnackbar('error', 'Błąd podczas pobierania pakietów');
      } finally {
        this.packagesLoader = false;
      }
    },
    async getCountries() {
      this.countriesLoader = true;
      try {
        const response = await this.axios.get('/api/lists/countries');
        if (response.status === 200 && response.data) {
          this.countries = response.data;
        }
      } catch (error) {
        this.showSnackbar('error', 'Błąd podczas pobierania krajów');
      } finally {
        this.countriesLoader = false;
      }
    },
  },
};
</script>
